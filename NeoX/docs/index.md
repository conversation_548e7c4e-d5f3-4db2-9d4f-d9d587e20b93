# 欢迎使用 NeoX 文档

本文档提供了 NeoX 系统的完整开发指南和相关资源。

## 📖 文档概览

NeoX 文档旨在帮助开发者快速上手和深入了解系统的各个方面，从环境搭建到具体开发实践，提供全方位的指导。

## 🚀 快速开始

### 开发环境搭建

完整的开发环境配置指南，帮助您快速搭建本地开发环境：

#### 后端开发

- **[代码部署](开发环境搭建/后端开发/代码部署.md)** - 获取项目代码和仓库权限配置

- **[AWS ECR权限设置](开发环境搭建/后端开发/AWS-ECR权限设置.md)** - 配置 AWS CLI 和 ECR 访问权限

- **[Docker环境部署](开发环境搭建/后端开发/Docker环境部署.md)** - 使用 Docker 搭建开发环境框架

- **[后端代码环境配置](开发环境搭建/后端开发/后端代码环境配置.md)** - 处方识别项目后端代码配置和验证

- **[常见问题解答](开发环境搭建/后端开发/FAQ.md)** - 开发环境搭建过程中的常见问题和解决方案

### SOP（标准操作程序）

标准化的操作流程和规范文档，确保团队协作的一致性和效率：

- **[项目研发管理流程规范](SOP/项目研发管理流程规范.md)** - 项目开发管理的标准化流程和规范

- **[测试及发布流程规范](SOP/测试及发布流程规范.md)** - 测试和发布操作的标准化程序

- **[产品版本命名规范](SOP/产品版本命名规范.md)** - 统一的版本控制和命名标准

### 自动化运维

自动化运维相关工具和平台的使用指南：

- **[Ansible Semaphore](自动化运维/自动化发布平台/Ansible-Semaphore.md)** - 自动化发布平台配置和使用

## 📋 文档导航

### 🔧 开发环境
| 章节        | 描述                   | 链接                                                  |
| ----------- | ---------------------- | ----------------------------------------------------- |
| 代码部署    | Git 仓库克隆和权限管理 | [查看详情](开发环境搭建/后端开发/代码部署.md)         |
| AWS ECR权限 | AWS 访问权限配置指南   | [查看详情](开发环境搭建/后端开发/AWS-ECR权限设置.md)  |
| Docker部署  | 容器化开发环境搭建     | [查看详情](开发环境搭建/后端开发/Docker环境部署.md)   |
| 后端配置    | 项目环境配置和验证     | [查看详情](开发环境搭建/后端开发/后端代码环境配置.md) |
| 常见问题    | 开发环境常见问题解答   | [查看详情](开发环境搭建/后端开发/FAQ.md)              |

### 📋 SOP（标准操作程序）
| 章节             | 描述                     | 链接                                    |
| ---------------- | ------------------------ | --------------------------------------- |
| 项目研发管理流程 | 项目开发管理标准化流程   | [查看详情](SOP/项目研发管理流程规范.md) |
| 测试及发布流程   | 测试和发布操作标准化程序 | [查看详情](SOP/测试及发布流程规范.md)   |
| 产品版本命名规范 | 统一的版本控制和命名标准 | [查看详情](SOP/产品版本命名规范.md)     |

### 🔄 自动化运维
| 章节              | 描述               | 链接                                                       |
| ----------------- | ------------------ | ---------------------------------------------------------- |
| Ansible Semaphore | 自动化发布平台配置 | [查看详情](自动化运维/自动化发布平台/Ansible-Semaphore.md) |

### 🔍 流程拓扑图

系统各模块的流程图和架构图，帮助理解系统运行机制和数据流向。

#### 🏥 医疗后端流程

| 章节                 | 描述                                                                     | 链接                                               |
| -------------------- | ------------------------------------------------------------------------ | -------------------------------------------------- |
| **GPU 引擎识别流程** | GPU 图像处理引擎流程图，包含图片分类、旋转、分割、OCR 识别等完整处理流程 | [查看详情](流程拓扑图/医疗后端/GPU引擎识别流程.md) |
| **NSIPS 系统**       | NSIPS 系统集成流程图（待完善）                                           | [查看详情](流程拓扑图/医疗后端/NSIPS.md)           |
| **Smart Merge**      | 智能合并处理流程图（待完善）                                             | [查看详情](流程拓扑图/医疗后端/Smart-Merge.md)     |

**识别端流程详细文档：**

| 序号 | 章节                 | 描述                                   | 链接                                                                     |
| ---- | -------------------- | -------------------------------------- | ------------------------------------------------------------------------ |
| 01   | **生命周期**         | 识别任务从创建到完成的完整生命周期流程 | [查看详情](流程拓扑图/医疗后端/识别端流程/01-生命周期.md)                |
| 02   | **整体架构**         | 识别服务的整体系统架构和组件关系       | [查看详情](流程拓扑图/医疗后端/识别端流程/02-整体架构.md)                |
| 03   | **识别任务生命周期** | 识别任务状态变化和处理流程详述         | [查看详情](流程拓扑图/医疗后端/识别端流程/03-识别任务的生命周期.md)      |
| 04   | **任务状态流转**     | 识别任务各状态之间的转换逻辑           | [查看详情](流程拓扑图/医疗后端/识别端流程/04-识别任务状态流转.md)        |
| 05   | **Redis 缓存**       | 用于结果轮询的 Redis 缓存机制          | [查看详情](流程拓扑图/医疗后端/识别端流程/05-用于结果轮询的Redis缓存.md) |
| 06   | **服务分层架构**     | 识别服务的分层架构和组件设计           | [查看详情](流程拓扑图/医疗后端/识别端流程/06-服务分层架构-组件.md)       |
| 07   | **Task 信息详述**    | 识别任务 Task 对象的详细信息结构       | [查看详情](流程拓扑图/医疗后端/识别端流程/07-识别任务task信息详述.md)    |
| 08   | **处方合并流程**     | 多页处方的智能合并处理流程             | [查看详情](流程拓扑图/医疗后端/识别端流程/08-处方合并流程.md)            |
| 09   | **FAX 受付流程**     | FAX 处方受付处理的完整流程图           | [查看详情](流程拓扑图/医疗后端/识别端流程/09-FAX受付流程.md)             |

#### 🤖 自动化运维流程

| 章节                         | 描述                                    | 链接                                                 |
| ---------------------------- | --------------------------------------- | ---------------------------------------------------- |
| **Medical Backend**          | 医疗后端服务的自动化部署和运维流程      | [查看详情](流程拓扑图/自动化运维/medical-backend.md) |
| **Performance Testing**      | 性能测试基础设施的自动化工作流程        | [查看详情](流程拓扑图/自动化运维/performance.md)     |
| **Terraform Infrastructure** | 基于 Terraform 的云基础设施管理工作流程 | [查看详情](流程拓扑图/自动化运维/terraform.md)       |

#### 💊 薬師丸賢太流程

| 章节                   | 描述                                       | 链接                                                    |
| ---------------------- | ------------------------------------------ | ------------------------------------------------------- |
| **处方笺保存匹配流程** | 薬師丸賢太系统中处方笺的保存和匹配处理流程 | [查看详情](流程拓扑图/薬師丸賢太/处方笺保存匹配流程.md) |

#### 🏪 スマート薬局流程

**薬師丸撫子**

| 章节                       | 描述                                                                        | 链接                                                                     |
| -------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------ |
| **扫描仪连接结果获取流程** | 扫描仪设备连接、扫描操作、PDF处理、QR识别、文件上传的完整自动化处理流程图解 | [查看详情](流程拓扑图/スマート薬局/薬師丸撫子/扫描仪连接结果获取流程.md) |

**通用模块**

| 章节                   | 描述                                                                     | 链接                                                               |
| ---------------------- | ------------------------------------------------------------------------ | ------------------------------------------------------------------ |
| **日志上传客户端流程** | 客户端日志自动上传系统的完整流程，包含系统信息轮询、文件压缩和上传处理等 | [查看详情](流程拓扑图/スマート薬局/通用模块/日志上传客户端流程.md) |

### 📚 系统信息
| 章节     | 描述                       | 链接                                       |
| -------- | -------------------------- | ------------------------------------------ |
| 版本说明 | 系统版本更新历史和变更记录 | [查看详情](关于/版本说明/release-notes.md) |

## 🎯 常用命令

### MkDocs 相关命令

```bash
# 创建新项目
mkdocs new [项目名称]

# 启动本地开发服务器
mkdocs serve

# 构建文档站点
mkdocs build

# 查看帮助信息
mkdocs -h
```

### Docker 相关命令

```bash
# 构建并启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止服务
docker-compose down
```

## 📁 项目结构

```
neox-docs/
├── NeoX/
│   ├── mkdocs.yml          # 文档配置文件
│   └── docs/               # 文档源文件
│       ├── index.md        # 文档首页
│       ├── 开发环境搭建/    # 开发环境配置
│       │   └── 后端开发/   # 后端开发指南
│       ├── 自动化运维/     # 自动化运维相关
│       │   └── 自动化发布平台/ # 发布平台配置
│       ├── 流程拓扑图/     # 系统流程图
│       │   ├── 医疗后端/   # 医疗后端流程
│       │   ├── 薬師丸賢太/ # 薬師丸賢太流程
│       │   └── 自动化运维/ # 运维流程
│       └── 关于/           # 系统信息
│           └── 版本说明/   # 版本更新记录
└── README.md               # 项目说明
```

## 🔍 如何使用本文档

1. **新手入门**：建议从 [代码部署](开发环境搭建/后端开发/代码部署.md) 开始

2. **环境配置**：按照 [后端开发](开发环境搭建/后端开发/AWS-ECR权限设置.md) 指南逐步操作

3. **标准流程**：查看 [SOP文档](SOP/项目研发管理流程规范.md) 了解标准化操作程序

4. **流程了解**：查看 [流程拓扑图](流程拓扑图/医疗后端/识别端流程/01-生命周期.md) 了解系统架构

5. **自动化部署**：参考 [自动化运维](自动化运维/自动化发布平台/Ansible-Semaphore.md) 指南

6. **问题排查**：查看 [常见问题](开发环境搭建/后端开发/FAQ.md) 解决常见问题

7. **版本信息**：查看 [版本说明](关于/版本说明/release-notes.md) 了解最新更新

## 💡 获取帮助

如果您在使用过程中遇到问题，请：

1. 首先查看相关章节的详细文档

2. 检查 [常见问题解答](开发环境搭建/后端开发/FAQ.md) 中是否有解决方案

3. 查看 [版本说明](关于/版本说明/release-notes.md) 中的已知问题

4. 联系开发团队获取技术支持

---

**当前版本**：v2025072901

**更新日期**：2025年7月29日

**维护团队**：NeoX 开发团队
