```mermaid
graph TB
    subgraph "更新后的NSIPS匹配流程 - 评分制单一结果"
        A[处方信息输入] --> B[1. 初步筛选<br/>获取所有NSIPS候选]
        
        B --> C{候选数量 > 0?}
        C -->|否| END1[❌ 返回空结果<br/>NO CANDIDATES]
        
        C -->|是| D[2. PeBeneficiaryNo 检查]
        D --> E{PeBeneficiaryNo<br/>匹配?}
        E -->|是| F[在PeBeneficiaryNo匹配中<br/>选择评分最高的一个]
        F --> END2[✅ 返回唯一结果<br/>Priority Match]
        
        E -->|否| G[3. 评分制匹配开始]
        
        subgraph "评分计算详情"
            G --> H[计算每个候选的综合评分]
            H --> I[NameKanji: 30分<br/>模糊匹配支持]
            H --> J[PeBearerNo: 25分<br/>精确匹配]
            H --> K[Birthday: 20分<br/>精确匹配]
            H --> L[HospitalCode: 15分<br/>精确匹配]
            H --> M[DocName: 5分<br/>精确匹配]
            H --> N[DeliveryDate: 3分<br/>灵活匹配]
            H --> O[DepartName: 2分<br/>模糊匹配]
            
            I --> P[累计总分]
            J --> P
            K --> P
            L --> P
            M --> P
            N --> P
            O --> P
        end
        
        P --> Q[4. 阈值筛选和排序]
        Q --> R{最高分 >= 40?}
        R -->|否| END3[❌ 无符合条件候选<br/>分数过低]
        R -->|是| S[选择评分最高的候选]
        
        S --> T[5. 详细日志输出]
        T --> U[显示最佳候选评分详情]
        T --> V[显示前3名候选对比]
        
        U --> END4[✅ 返回唯一最佳结果<br/>Best Scoring Match]
        V --> END4
        
        subgraph "核心优势"
            ADV1[🎯 始终返回单一结果<br/>避免多选困扰]
            ADV2[🛡 OCR错误容错<br/>姓名模糊匹配]
            ADV3[📊 透明评分机制<br/>可调试可优化]
            ADV4[⚡ 权重化匹配<br/>重要字段优先]
        end
        
        subgraph "评分策略"
            SCORE1[完全匹配: 满分]
            SCORE2[相似匹配: 按相似度给分]
            SCORE3[缺失字段: 不扣分不加分]
            SCORE4[不匹配: 0分]
        end
    end
    
    style A fill:#e3f2fd,stroke:#1976d2
    style END2 fill:#c8e6c9,stroke:#388e3c
    style END4 fill:#c8e6c9,stroke:#388e3c
    style END1 fill:#ffcdd2,stroke:#d32f2f
    style END3 fill:#ffcdd2,stroke:#d32f2f
    
    style ADV1 fill:#f3e5f5,stroke:#7b1fa2
    style ADV2 fill:#e8f5e8,stroke:#2e7d32
    style ADV3 fill:#e1f5fe,stroke:#0277bd
    style ADV4 fill:#fff8e1,stroke:#f57f17
    
    style I fill:#ff5722,color:#fff
    style J fill:#ff9800
    style K fill:#ffc107
    style L fill:#4caf50
    style M fill:#2196f3
    style N fill:#9c27b0
    style O fill:#607d8b
```
