
<!doctype html>
<html lang="zh" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Docs site for NeoX.">
      
      
        <meta name="author" content="SongLin Lu">
      
      
      
        <link rel="prev" href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/terraform.html">
      
      
      
      <link rel="icon" href="../../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.15">
    
    
      
        <title>版本说明 - NeoX Docs</title>
      
    
    
      <link rel="stylesheet" href="../../assets/stylesheets/main.342714a4.min.css">
      
        
        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
      
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#_1" class="md-skip">
          跳转至
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="页眉">
    <a href="../../index.html" title="NeoX Docs" class="md-header__button md-logo" aria-label="NeoX Docs" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            NeoX Docs
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              版本说明
            
          </span>
        </div>
      </div>
    </div>
    
      
        <form class="md-header__option" data-md-component="palette">
  
    
    
    
    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="blue" data-md-color-accent="blue"  aria-hidden="true"  type="radio" name="__palette" id="__palette_0">
    
  
</form>
      
    
    
      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
    
    
    
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="标签" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
  
    <li class="md-tabs__item">
      <a href="../../index.html" class="md-tabs__link">
        
  
  
    
  
  主页

      </a>
    </li>
  

      
        
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-tabs__link">
          
  
  
  SOP

        </a>
      </li>
    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html" class="md-tabs__link">
          
  
  
  开发环境搭建

        </a>
      </li>
    
  

    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html" class="md-tabs__link">
          
  
  
  自动化运维

        </a>
      </li>
    
  

    
  

      
        
  
  
  
  
    
    
      
  
  
  
  
    
    
      
  
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-tabs__link">
          
  
  
  流程拓扑图

        </a>
      </li>
    
  

    
  

    
  

      
        
  
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="release-notes.html" class="md-tabs__link">
          
  
  
  关于

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


  

<nav class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" aria-label="导航栏" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../../index.html" title="NeoX Docs" class="md-nav__button md-logo" aria-label="NeoX Docs" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"/></svg>

    </a>
    NeoX Docs
  </label>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href="../../index.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    主页
    
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    SOP
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            SOP
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    项目研发管理流程规范
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SOP/%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../SOP/%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    产品版本命名规范
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    开发环境搭建
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            开发环境搭建
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3_1" >
        
          
          <label class="md-nav__link" for="__nav_3_1" id="__nav_3_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    后端开发
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_3_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3_1">
            <span class="md-nav__icon md-icon"></span>
            后端开发
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    代码部署
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/AWS-ECR%E6%9D%83%E9%99%90%E8%AE%BE%E7%BD%AE.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    AWS ECR权限设置
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/Docker%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Docker环境部署
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E5%90%8E%E7%AB%AF%E4%BB%A3%E7%A0%81%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    后端代码环境配置
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/FAQ.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    常见问题解答
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化运维
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4_1" >
        
          
          <label class="md-nav__link" for="__nav_4_1" id="__nav_4_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化发布平台
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_4_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4_1">
            <span class="md-nav__icon md-icon"></span>
            自动化发布平台
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Ansible Semaphore
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >
        
          
          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    流程拓扑图
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            流程拓扑图
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_1" >
        
          
          <label class="md-nav__link" for="__nav_5_1" id="__nav_5_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    医疗后端
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_1">
            <span class="md-nav__icon md-icon"></span>
            医疗后端
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_1_1" >
        
          
          <label class="md-nav__link" for="__nav_5_1_1" id="__nav_5_1_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    识别端流程
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_1_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_1_1">
            <span class="md-nav__icon md-icon"></span>
            识别端流程
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    01-生命周期
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/02-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    02-整体架构
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    03-识别任务的生命周期
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/04-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    04-识别任务状态流转
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/05-%E7%94%A8%E4%BA%8E%E7%BB%93%E6%9E%9C%E8%BD%AE%E8%AF%A2%E7%9A%84Redis%E7%BC%93%E5%AD%98.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    05-用于结果轮询的Redis缓存
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/06-%E6%9C%8D%E5%8A%A1%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84-%E7%BB%84%E4%BB%B6.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    06-服务分层架构-组件
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/07-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1task%E4%BF%A1%E6%81%AF%E8%AF%A6%E8%BF%B0.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    07-识别任务task信息详述
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/08-%E5%A4%84%E6%96%B9%E5%90%88%E5%B9%B6%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    08-处方合并流程
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/09-FAX%E5%8F%97%E4%BB%98%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    09-FAX受付流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/NSIPS.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    NSIPS
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/GPU%E5%BC%95%E6%93%8E%E8%AF%86%E5%88%AB%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    GPU 引擎识别流程
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/Smart-Merge.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    Smart Merge
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_2" >
        
          
          <label class="md-nav__link" for="__nav_5_2" id="__nav_5_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    薬師丸賢太
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_2">
            <span class="md-nav__icon md-icon"></span>
            薬師丸賢太
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%96%AC%E5%B8%AB%E4%B8%B8%E8%B3%A2%E5%A4%AA/%E5%A4%84%E6%96%B9%E7%AC%BA%E4%BF%9D%E5%AD%98%E5%8C%B9%E9%85%8D%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    处方笺保存匹配流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3" >
        
          
          <label class="md-nav__link" for="__nav_5_3" id="__nav_5_3_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    スマート薬局
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3">
            <span class="md-nav__icon md-icon"></span>
            スマート薬局
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3_1" >
        
          
          <label class="md-nav__link" for="__nav_5_3_1" id="__nav_5_3_1_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    薬師丸撫子
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_3_1_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3_1">
            <span class="md-nav__icon md-icon"></span>
            薬師丸撫子
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E8%96%AC%E5%B8%AB%E4%B8%B8%E6%92%AB%E5%AD%90/%E6%89%AB%E6%8F%8F%E4%BB%AA%E8%BF%9E%E6%8E%A5%E7%BB%93%E6%9E%9C%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    扫描仪连接结果获取流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_3_2" >
        
          
          <label class="md-nav__link" for="__nav_5_3_2" id="__nav_5_3_2_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    通用模块
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="3" aria-labelledby="__nav_5_3_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_3_2">
            <span class="md-nav__icon md-icon"></span>
            通用模块
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    日志上传客户端流程
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
              
                
  
  
  
  
    
    
    
    
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--nested">
      
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5_4" >
        
          
          <label class="md-nav__link" for="__nav_5_4" id="__nav_5_4_label" tabindex="0">
            
  
  
  <span class="md-ellipsis">
    自动化运维
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="2" aria-labelledby="__nav_5_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5_4">
            <span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/medical-backend.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    medical-backend
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/performance.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    performance
    
  </span>
  

      </a>
    </li>
  

              
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../../%E6%B5%81%E7%A8%8B%E6%8B%93%E6%89%91%E5%9B%BE/%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/terraform.html" class="md-nav__link">
        
  
  
  <span class="md-ellipsis">
    terraform
    
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
      
        
        
      
      
        
      
    
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_6" checked>
        
          
          <label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="">
            
  
  
  <span class="md-ellipsis">
    关于
    
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_6_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_6">
            <span class="md-nav__icon md-icon"></span>
            关于
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  
  <span class="md-ellipsis">
    版本说明
    
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="release-notes.html" class="md-nav__link md-nav__link--active">
        
  
  
  <span class="md-ellipsis">
    版本说明
    
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="目录">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      目录
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#v20250729012025729-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025072901（2025年7月29日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025072901（2025年7月29日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_2" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 新功能
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_3" class="md-nav__link">
    <span class="md-ellipsis">
      📝 导航结构优化
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_4" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 文档结构完善
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_5" class="md-nav__link">
    <span class="md-ellipsis">
      ✅ 质量保证
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250728022025728-2" class="md-nav__link">
    <span class="md-ellipsis">
      v2025072802（2025年7月28日 第2版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025072802（2025年7月28日 第2版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_6" class="md-nav__link">
    <span class="md-ellipsis">
      🔄 处方合并流程优化
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_7" class="md-nav__link">
    <span class="md-ellipsis">
      ⚡ 性能优化改进
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_8" class="md-nav__link">
    <span class="md-ellipsis">
      📝 流程文档更新
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250728012025728-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025072801（2025年7月28日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025072801（2025年7月28日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_9" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 流程图标准化优化
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_10" class="md-nav__link">
    <span class="md-ellipsis">
      📝 流程优化改进
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_11" class="md-nav__link">
    <span class="md-ellipsis">
      ✅ 质量保证
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250723012025723-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025072301（2025年7月23日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025072301（2025年7月23日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_12" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 新功能
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_13" class="md-nav__link">
    <span class="md-ellipsis">
      🎨 流程图标准化优化
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_14" class="md-nav__link">
    <span class="md-ellipsis">
      📝 语法现代化改进
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_15" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 语法兼容性修复
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_16" class="md-nav__link">
    <span class="md-ellipsis">
      ✅ 质量保证
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250721012025721-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025072101（2025年7月21日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025072101（2025年7月21日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_17" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 新功能
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_18" class="md-nav__link">
    <span class="md-ellipsis">
      📝 内容改进
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_19" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 文档结构完善
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_20" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 文档覆盖范围扩展
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250630012025630-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025063001（2025年6月30日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025063001（2025年6月30日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_21" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 新功能
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_22" class="md-nav__link">
    <span class="md-ellipsis">
      📝 内容改进
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_23" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 文档结构完善
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_24" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 问题解决覆盖
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#v20250628012025628-1" class="md-nav__link">
    <span class="md-ellipsis">
      v2025062801（2025年6月28日 第1版）
    </span>
  </a>
  
    <nav class="md-nav" aria-label="v2025062801（2025年6月28日 第1版）">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#_25" class="md-nav__link">
    <span class="md-ellipsis">
      🚀 新功能
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_26" class="md-nav__link">
    <span class="md-ellipsis">
      📚 文档结构
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_27" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 技术特性
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_28" class="md-nav__link">
    <span class="md-ellipsis">
      📝 文档覆盖范围
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#_29" class="md-nav__link">
    <span class="md-ellipsis">
      🎯 下一步计划
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  



<h1 id="_1">版本说明<a class="headerlink" href="#_1" title="Permanent link">&para;</a></h1>
<p>本文档记录了 NeoX 文档系统的版本更新历史和重要变更。</p>
<h2 id="v20250729012025729-1">v2025072901（2025年7月29日 第1版）<a class="headerlink" href="#v20250729012025729-1" title="Permanent link">&para;</a></h2>
<h3 id="_2">🚀 新功能<a class="headerlink" href="#_2" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>SOP文档体系建立</strong></p>
</li>
<li>
<p>新增项目研发管理流程规范文档，规范化项目开发管理标准流程</p>
</li>
<li>
<p>新增测试及发布流程规范文档，标准化测试和发布操作程序</p>
</li>
<li>
<p>新增产品版本命名规范文档，建立统一的版本控制和命名标准</p>
</li>
</ul>
<h3 id="_3">📝 导航结构优化<a class="headerlink" href="#_3" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>SOP章节添加</strong></p>
</li>
<li>
<p>在文档导航中新增SOP（标准操作程序）专门章节</p>
</li>
<li>
<p>提供3个核心SOP文档的快速访问链接</p>
</li>
<li>
<p>改善文档的可发现性和组织结构</p>
</li>
</ul>
<h3 id="_4">🔧 文档结构完善<a class="headerlink" href="#_4" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>标准化流程建立</strong></p>
</li>
<li>
<p>建立完整的标准操作程序文档体系</p>
</li>
<li>
<p>涵盖项目管理、测试、发布和版本控制等关键环节</p>
</li>
<li>
<p>为NeoX项目提供统一的工作流程规范</p>
</li>
</ul>
<h3 id="_5">✅ 质量保证<a class="headerlink" href="#_5" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>流程标准化</strong></p>
</li>
<li>
<p>确保所有团队成员遵循统一的操作标准</p>
</li>
<li>
<p>提高项目管理和开发流程的一致性</p>
</li>
<li>
<p>增强团队协作效率和项目交付质量</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年7月29日</p>
<p><strong>版本类型</strong>：功能增强版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250728022025728-2">v2025072802（2025年7月28日 第2版）<a class="headerlink" href="#v20250728022025728-2" title="Permanent link">&para;</a></h2>
<h3 id="_6">🔄 处方合并流程优化<a class="headerlink" href="#_6" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>合并策略重构</strong></p>
</li>
<li>
<p>将即时合并模式改为批量合并模式，提高处理效率</p>
</li>
<li>
<p>多个"调用 mergeTaskToQR"和"调用 mergeTaskToOCR"步骤统一改为"记录合并的taskId"</p>
</li>
<li>
<p>最终通过"调用combineTaskData批量合并taskIds"进行统一批量处理</p>
</li>
</ul>
<h3 id="_7">⚡ 性能优化改进<a class="headerlink" href="#_7" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>批量处理机制</strong></p>
</li>
<li>
<p>减少频繁的合并调用，降低系统资源消耗</p>
</li>
<li>
<p>优化大批量处方处理的性能表现</p>
</li>
<li>
<p>提升整体处理效率和系统稳定性</p>
</li>
</ul>
<h3 id="_8">📝 流程文档更新<a class="headerlink" href="#_8" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>处方合并流程图更新</strong></p>
</li>
<li>
<p>更新08-处方合并流程.md文档，反映最新的批量合并实现逻辑</p>
</li>
<li>
<p>保持流程图与实际代码实现的一致性</p>
</li>
<li>
<p>确保文档的准确性和实用性</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年7月28日</p>
<p><strong>版本类型</strong>：性能优化版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250728012025728-1">v2025072801（2025年7月28日 第1版）<a class="headerlink" href="#v20250728012025728-1" title="Permanent link">&para;</a></h2>
<h3 id="_9">🎨 流程图标准化优化<a class="headerlink" href="#_9" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>NSIPS 匹配服务流程图更新</strong></p>
</li>
<li>
<p>对处方笺保存匹配流程.md进行样式类标准化优化</p>
</li>
<li>
<p>更新样式类定义，采用模板标准颜色方案，提高视觉一致性</p>
</li>
<li>
<p>重新分配样式类应用，根据实际节点功能进行语义化分类</p>
</li>
<li>
<p>优化流程图的视觉层次和可读性</p>
</li>
</ul>
<h3 id="_10">📝 流程优化改进<a class="headerlink" href="#_10" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>智能药局扫描仪流程优化</strong></p>
</li>
<li>
<p>移除QR预处理步骤，简化扫描仪连接结果获取流程</p>
</li>
<li>
<p>优化处理逻辑结构，提高流程执行效率</p>
</li>
<li>
<p>减少不必要的处理环节，提升用户体验</p>
</li>
</ul>
<h3 id="_11">✅ 质量保证<a class="headerlink" href="#_11" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>文档一致性维护</strong></p>
</li>
<li>
<p>保持所有流程逻辑和业务功能的完整性</p>
</li>
<li>
<p>确保样式标准化不影响原有流程的准确性</p>
</li>
<li>
<p>维护文档的专业性和技术准确性</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年7月28日</p>
<p><strong>版本类型</strong>：流程优化版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250723012025723-1">v2025072301（2025年7月23日 第1版）<a class="headerlink" href="#v20250723012025723-1" title="Permanent link">&para;</a></h2>
<h3 id="_12">🚀 新功能<a class="headerlink" href="#_12" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>流程拓扑图章节</strong></p>
</li>
<li>
<p>新增スマート薬局相关流程文档，包含薬師丸撫子和通用模块流程</p>
</li>
<li>
<p>新增扫描仪连接结果获取流程详细文档，涵盖设备连接、扫描操作、PDF处理、QR识别、文件上传等完整自动化处理流程</p>
</li>
<li>
<p>新增日志上传客户端流程文档，包含系统信息轮询、文件压缩和上传处理等完整流程</p>
</li>
</ul>
<h3 id="_13">🎨 流程图标准化优化<a class="headerlink" href="#_13" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>Mermaid 流程图规范化</strong></p>
</li>
<li>
<p>对扫描仪连接结果获取流程.md文件进行了完整的标准化优化</p>
</li>
<li>
<p>对日志上传客户端流程.md文件进行了完整的标准化优化</p>
</li>
</ul>
<h3 id="_14">📝 语法现代化改进<a class="headerlink" href="#_14" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>语法更新</strong></p>
</li>
<li>
<p>将 <code>graph TD</code> 更新为现代 <code>flowchart TD</code> 语法</p>
</li>
<li>
<p>统一使用 <code>--&gt;|文本|</code> 替代旧的 <code>-- 文本 --&gt;</code> 连接语法</p>
</li>
<li>
<p>规范化节点形状：开始/结束使用 <code>([])</code>, 处理步骤使用 <code>[]</code>, 决策点使用 <code>{}</code></p>
</li>
<li>
<p><strong>样式标准化</strong></p>
</li>
<li>
<p>应用统一的 <code>classDef</code> 样式类定义系统</p>
</li>
<li>
<p>使用模板标准颜色方案（开始/结束：深蓝色 #1565c0，处理步骤：紫色 #6a1b9a，决策点：橙色 #ef6c00，错误：红色 #b71c1c）</p>
</li>
<li>
<p>移除所有旧的内联样式，使用 <code>class</code> 应用样式类</p>
</li>
</ul>
<h3 id="_15">🔧 语法兼容性修复<a class="headerlink" href="#_15" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>特殊字符处理</strong></p>
</li>
<li>
<p>对包含括号 <code>()</code> 和下划线 <code>_</code> 的节点标签使用双引号包围</p>
</li>
<li>
<p>修复了 Mermaid 解析器的语法冲突问题</p>
</li>
<li>
<p>确保所有流程图能够正常预览和渲染</p>
</li>
</ul>
<h3 id="_16">✅ 质量保证<a class="headerlink" href="#_16" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>流程完整性维护</strong></p>
</li>
<li>
<p>保持所有原有流程逻辑和连接关系不变</p>
</li>
<li>
<p>维护所有 subgraph 分组结构的完整性</p>
</li>
<li>
<p>确保业务流程的准确性和可读性</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年7月23日</p>
<p><strong>版本类型</strong>：流程图标准化版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250721012025721-1">v2025072101（2025年7月21日 第1版）<a class="headerlink" href="#v20250721012025721-1" title="Permanent link">&para;</a></h2>
<h3 id="_17">🚀 新功能<a class="headerlink" href="#_17" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>流程拓扑图章节</strong></p>
</li>
<li>
<p>新增医疗后端识别端流程详细文档，包含生命周期、架构、状态流转等</p>
</li>
<li>
<p>新增 FAX 受付流程、GPU引擎识别流程、NSIPS、Smart Merge 等医疗后端相关流程</p>
</li>
<li>
<p>新增薬師丸賢太处方笺保存匹配流程文档</p>
</li>
<li>
<p>新增自动化运维相关流程文档，包括 medical-backend、performance、terraform</p>
</li>
<li>
<p><strong>自动化运维章节</strong></p>
</li>
<li>
<p>新增 Ansible Semaphore 自动化发布平台配置指南</p>
</li>
</ul>
<h3 id="_18">📝 内容改进<a class="headerlink" href="#_18" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>文档导航结构优化</strong></p>
</li>
<li>
<p>重新组织导航结构，增加主要章节分类</p>
</li>
<li>
<p>优化子章节组织，提高文档查找效率</p>
</li>
<li>
<p>调整文档层级，使结构更加清晰</p>
</li>
<li>
<p><strong>首页内容更新</strong></p>
</li>
<li>
<p>更新首页导航表格，增加新增章节的快速链接</p>
</li>
<li>
<p>完善项目结构描述，反映最新的文件组织</p>
</li>
<li>
<p>优化"如何使用本文档"部分，增加流程了解和自动化部署指南</p>
</li>
</ul>
<h3 id="_19">🔧 文档结构完善<a class="headerlink" href="#_19" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>导航配置更新</strong></p>
</li>
<li>
<p>在 <code>mkdocs.yml</code> 中完善导航配置，增加新章节</p>
</li>
<li>
<p>调整章节顺序，优化用户浏览体验</p>
</li>
<li>
<p>保持导航结构与实际文件结构一致</p>
</li>
<li>
<p><strong>README 同步更新</strong></p>
</li>
<li>
<p>更新项目主要内容描述，增加自动化运维和流程拓扑图内容</p>
</li>
<li>
<p>完善项目结构图，反映最新的目录组织</p>
</li>
<li>
<p>增加获取帮助部分的常见问题解答链接</p>
</li>
</ul>
<h3 id="_20">🎯 文档覆盖范围扩展<a class="headerlink" href="#_20" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>系统架构文档</strong>：新增多个系统架构和流程图文档，帮助开发者理解系统整体结构</p>
</li>
<li>
<p><strong>自动化工具文档</strong>：增加自动化部署和运维相关工具的使用指南</p>
</li>
<li>
<p><strong>流程说明文档</strong>：详细说明各个子系统的工作流程和数据流转</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年7月21日</p>
<p><strong>版本类型</strong>：功能扩展版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250630012025630-1">v2025063001（2025年6月30日 第1版）<a class="headerlink" href="#v20250630012025630-1" title="Permanent link">&para;</a></h2>
<h3 id="_21">🚀 新功能<a class="headerlink" href="#_21" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>常见问题解答（FAQ）</strong></p>
</li>
<li>
<p>新增详细的FAQ文档，涵盖开发环境搭建中的常见问题</p>
</li>
<li>
<p>包含Docker容器报错、API请求鉴权、网络连接问题等解决方案</p>
</li>
<li>
<p>提供Windows系统特有问题的解决方法</p>
</li>
</ul>
<h3 id="_22">📝 内容改进<a class="headerlink" href="#_22" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>项目描述优化</strong></p>
</li>
<li>
<p>简化项目介绍，提高可读性</p>
</li>
<li>
<p>更新仓库地址为具体的Bitbucket链接</p>
</li>
<li>
<p>优化项目结构描述，使用通用格式</p>
</li>
<li>
<p><strong>使用方式重构</strong></p>
</li>
<li>
<p>重新组织README.md中的使用者和开发者说明</p>
</li>
<li>
<p>使用者现在可直接打开HTML文件，无需安装任何工具</p>
</li>
<li>
<p>开发者提供多种安装和启动方式选择</p>
</li>
</ul>
<h3 id="_23">🔧 文档结构完善<a class="headerlink" href="#_23" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>导航优化</strong></p>
</li>
<li>
<p>在主页添加FAQ章节的快速访问链接</p>
</li>
<li>
<p>更新文档导航表格，包含常见问题解答条目</p>
</li>
<li>
<p>保持文档结构的一致性和完整性</p>
</li>
</ul>
<h3 id="_24">🎯 问题解决覆盖<a class="headerlink" href="#_24" title="Permanent link">&para;</a></h3>
<p>FAQ文档包含以下常见问题的解决方案：</p>
<ul>
<li>
<p><strong>Docker容器报错处理</strong>：composer扩展安装和.env配置</p>
</li>
<li>
<p><strong>API请求鉴权设置</strong>：公私钥对配置和Postman使用</p>
</li>
<li>
<p><strong>ImageMagick挂载错误</strong>：policy.xml挂载问题解决</p>
</li>
<li>
<p><strong>Windows网络连接问题</strong>：代理设置和手动安装方法</p>
</li>
<li>
<p><strong>Composer安装报错</strong>：依赖冲突和SSH密钥配置</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年6月30日</p>
<p><strong>版本类型</strong>：功能增强版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>
<h2 id="v20250628012025628-1">v2025062801（2025年6月28日 第1版）<a class="headerlink" href="#v20250628012025628-1" title="Permanent link">&para;</a></h2>
<h3 id="_25">🚀 新功能<a class="headerlink" href="#_25" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>文档系统初始化</strong></p>
</li>
<li>
<p>建立了完整的开发文档体系</p>
</li>
<li>
<p>采用 MkDocs Material 主题，提供现代化的文档界面</p>
</li>
<li>
<p>支持中文本地化显示</p>
</li>
<li>
<p><strong>后端开发环境搭建指南</strong></p>
</li>
<li>
<p>详细的代码部署流程说明</p>
</li>
<li>
<p>AWS ECR 访问权限配置指导</p>
</li>
<li>
<p>Docker 开发环境部署步骤</p>
</li>
<li>
<p>后端代码环境配置详解</p>
</li>
</ul>
<h3 id="_26">📚 文档结构<a class="headerlink" href="#_26" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>开发环境搭建</strong></p>
</li>
<li>
<p>后端开发完整流程文档</p>
</li>
<li>
<p>分模块详细说明，便于查阅和维护</p>
</li>
<li>
<p><strong>版本管理</strong></p>
</li>
<li>
<p>建立规范的版本说明文档</p>
</li>
<li>
<p>采用日期版本号格式（YYYYMMDDXX）</p>
</li>
</ul>
<h3 id="_27">🔧 技术特性<a class="headerlink" href="#_27" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>文档框架</strong>：MkDocs + Material 主题</p>
</li>
<li>
<p><strong>语言支持</strong>：完整中文支持</p>
</li>
<li>
<p><strong>导航结构</strong>：层级化章节组织</p>
</li>
<li>
<p><strong>搜索功能</strong>：全文搜索支持</p>
</li>
<li>
<p><strong>响应式设计</strong>：适配各种设备屏幕</p>
</li>
</ul>
<h3 id="_28">📝 文档覆盖范围<a class="headerlink" href="#_28" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>代码部署</strong>：Git 仓库管理和代码获取</p>
</li>
<li>
<p><strong>权限配置</strong>：AWS ECR 访问权限设置</p>
</li>
<li>
<p><strong>环境搭建</strong>：Docker 开发环境部署</p>
</li>
<li>
<p><strong>项目配置</strong>：后端服务配置和验证</p>
</li>
</ul>
<h3 id="_29">🎯 下一步计划<a class="headerlink" href="#_29" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p>添加更多开发环境配置说明</p>
</li>
<li>
<p>完善API文档</p>
</li>
<li>
<p>增加常见问题解答（FAQ）</p>
</li>
<li>
<p>补充最佳实践指南</p>
</li>
</ul>
<hr />
<p><strong>发布日期</strong>：2025年6月28日</p>
<p><strong>版本类型</strong>：初始版本</p>
<p><strong>维护团队</strong>：NeoX 开发团队</p>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
          <button type="button" class="md-top md-icon" data-md-component="top" hidden>
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  回到页面顶部
</button>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
    <div class="md-copyright__highlight">
      Copyright © 2025 SongLin Lu. All rights reserved.
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    
      
      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.sections", "navigation.expand", "navigation.tabs", "navigation.top", "navigation.tracking", "toc.follow", "toc.integrate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": null}</script>
    
    
      <script src="../../assets/javascripts/bundle.56ea9cef.min.js"></script>
      
    
  </body>
</html>