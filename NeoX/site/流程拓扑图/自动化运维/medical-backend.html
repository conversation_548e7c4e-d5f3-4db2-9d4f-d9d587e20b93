
<!DOCTYPE html>

<html class="no-js" lang="zh">
<head>
<meta charset="utf-8"/>
<meta content="width=device-width,initial-scale=1" name="viewport"/>
<meta content="Docs site for NeoX." name="description"/>
<meta content="SongLin Lu" name="author"/>
<link href="../%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html" rel="prev"/>
<link href="performance.html" rel="next"/>
<link href="../../assets/images/favicon.png" rel="icon"/>
<meta content="mkdocs-1.6.1, mkdocs-material-9.6.15" name="generator"/>
<title>medical-backend - NeoX Docs</title>
<link href="../../assets/stylesheets/main.342714a4.min.css" rel="stylesheet"/>
<link href="../../assets/stylesheets/palette.06af60db.min.css" rel="stylesheet"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&amp;display=fallback" rel="stylesheet"/>
<style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
<script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
</head>
<body data-md-color-accent="blue" data-md-color-primary="blue" data-md-color-scheme="default" dir="ltr">
<input autocomplete="off" class="md-toggle" data-md-toggle="drawer" id="__drawer" type="checkbox"/>
<input autocomplete="off" class="md-toggle" data-md-toggle="search" id="__search" type="checkbox"/>
<label class="md-overlay" for="__drawer"></label>
<div data-md-component="skip">
<a class="md-skip" href="#medical-backend-infrastructure-workflow">
          跳转至
        </a>
</div>
<div data-md-component="announce">
</div>
<header class="md-header" data-md-component="header">
<nav aria-label="页眉" class="md-header__inner md-grid">
<a aria-label="NeoX Docs" class="md-header__button md-logo" data-md-component="logo" href="../../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
<label class="md-header__button md-icon" for="__drawer">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"></path></svg>
</label>
<div class="md-header__title" data-md-component="header-title">
<div class="md-header__ellipsis">
<div class="md-header__topic">
<span class="md-ellipsis">
            NeoX Docs
          </span>
</div>
<div class="md-header__topic" data-md-component="header-topic">
<span class="md-ellipsis">
            
              medical-backend
            
          </span>
</div>
</div>
</div>
<form class="md-header__option" data-md-component="palette">
<input aria-hidden="true" class="md-option" data-md-color-accent="blue" data-md-color-media="" data-md-color-primary="blue" data-md-color-scheme="default" id="__palette_0" name="__palette" type="radio"/>
</form>
<script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>
</nav>
</header>
<div class="md-container" data-md-component="container">
<nav aria-label="标签" class="md-tabs" data-md-component="tabs">
<div class="md-grid">
<ul class="md-tabs__list">
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../index.html">
        
  
  
    
  
  主页

      </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
          
  
  
  SOP

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
          
  
  
  开发环境搭建

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
          
  
  
  自动化运维

        </a>
</li>
<li class="md-tabs__item md-tabs__item--active">
<a class="md-tabs__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
          
  
  
  流程拓扑图

        </a>
</li>
<li class="md-tabs__item">
<a class="md-tabs__link" href="../../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
          
  
  
  关于

        </a>
</li>
</ul>
</div>
</nav>
<main class="md-main" data-md-component="main">
<div class="md-main__inner md-grid">
<div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation">
<div class="md-sidebar__scrollwrap">
<div class="md-sidebar__inner">
<nav aria-label="导航栏" class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" data-md-level="0">
<label class="md-nav__title" for="__drawer">
<a aria-label="NeoX Docs" class="md-nav__button md-logo" data-md-component="logo" href="../../index.html" title="NeoX Docs">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54"></path></svg>
</a>
    NeoX Docs
  </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../index.html">
<span class="md-ellipsis">
    主页
    
  </span>
</a>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">
<span class="md-ellipsis">
    SOP
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_2_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_2">
<span class="md-nav__icon md-icon"></span>
            SOP
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../SOP/%E9%A1%B9%E7%9B%AE%E7%A0%94%E5%8F%91%E7%AE%A1%E7%90%86%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    项目研发管理流程规范
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../SOP/%E6%B5%8B%E8%AF%95%E5%8F%8A%E5%8F%91%E5%B8%83%E6%B5%81%E7%A8%8B%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    测试及发布流程规范
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../SOP/%E4%BA%A7%E5%93%81%E7%89%88%E6%9C%AC%E5%91%BD%E5%90%8D%E8%A7%84%E8%8C%83.html">
<span class="md-ellipsis">
    产品版本命名规范
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">
<span class="md-ellipsis">
    开发环境搭建
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_3">
<span class="md-nav__icon md-icon"></span>
            开发环境搭建
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_3_1" id="__nav_3_1_label" tabindex="0">
<span class="md-ellipsis">
    后端开发
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_3_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_3_1">
<span class="md-nav__icon md-icon"></span>
            后端开发
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E4%BB%A3%E7%A0%81%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    代码部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/AWS-ECR%E6%9D%83%E9%99%90%E8%AE%BE%E7%BD%AE.html">
<span class="md-ellipsis">
    AWS ECR权限设置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/Docker%E7%8E%AF%E5%A2%83%E9%83%A8%E7%BD%B2.html">
<span class="md-ellipsis">
    Docker环境部署
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/%E5%90%8E%E7%AB%AF%E4%BB%A3%E7%A0%81%E7%8E%AF%E5%A2%83%E9%85%8D%E7%BD%AE.html">
<span class="md-ellipsis">
    后端代码环境配置
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%BC%80%E5%8F%91%E7%8E%AF%E5%A2%83%E6%90%AD%E5%BB%BA/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/FAQ.html">
<span class="md-ellipsis">
    常见问题解答
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_4_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_4_1" id="__nav_4_1_label" tabindex="0">
<span class="md-ellipsis">
    自动化发布平台
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_4_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_4_1">
<span class="md-nav__icon md-icon"></span>
            自动化发布平台
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E8%87%AA%E5%8A%A8%E5%8C%96%E8%BF%90%E7%BB%B4/%E8%87%AA%E5%8A%A8%E5%8C%96%E5%8F%91%E5%B8%83%E5%B9%B3%E5%8F%B0/Ansible-Semaphore.html">
<span class="md-ellipsis">
    Ansible Semaphore
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_5" type="checkbox"/>
<label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="">
<span class="md-ellipsis">
    流程拓扑图
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_5_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_5">
<span class="md-nav__icon md-icon"></span>
            流程拓扑图
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1" id="__nav_5_1_label" tabindex="">
<span class="md-ellipsis">
    医疗后端
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_1_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_1">
<span class="md-nav__icon md-icon"></span>
            医疗后端
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_1_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_1_1" id="__nav_5_1_1_label" tabindex="0">
<span class="md-ellipsis">
    识别端流程
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_1_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_1_1">
<span class="md-nav__icon md-icon"></span>
            识别端流程
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/01-%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    01-生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/02-%E6%95%B4%E4%BD%93%E6%9E%B6%E6%9E%84.html">
<span class="md-ellipsis">
    02-整体架构
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/03-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%9A%84%E7%94%9F%E5%91%BD%E5%91%A8%E6%9C%9F.html">
<span class="md-ellipsis">
    03-识别任务的生命周期
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/04-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1%E7%8A%B6%E6%80%81%E6%B5%81%E8%BD%AC.html">
<span class="md-ellipsis">
    04-识别任务状态流转
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/05-%E7%94%A8%E4%BA%8E%E7%BB%93%E6%9E%9C%E8%BD%AE%E8%AF%A2%E7%9A%84Redis%E7%BC%93%E5%AD%98.html">
<span class="md-ellipsis">
    05-用于结果轮询的Redis缓存
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/06-%E6%9C%8D%E5%8A%A1%E5%88%86%E5%B1%82%E6%9E%B6%E6%9E%84-%E7%BB%84%E4%BB%B6.html">
<span class="md-ellipsis">
    06-服务分层架构-组件
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/07-%E8%AF%86%E5%88%AB%E4%BB%BB%E5%8A%A1task%E4%BF%A1%E6%81%AF%E8%AF%A6%E8%BF%B0.html">
<span class="md-ellipsis">
    07-识别任务task信息详述
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/08-%E5%A4%84%E6%96%B9%E5%90%88%E5%B9%B6%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    08-处方合并流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/%E8%AF%86%E5%88%AB%E7%AB%AF%E6%B5%81%E7%A8%8B/09-FAX%E5%8F%97%E4%BB%98%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    09-FAX受付流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/NSIPS.html">
<span class="md-ellipsis">
    NSIPS
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/GPU%E5%BC%95%E6%93%8E%E8%AF%86%E5%88%AB%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    GPU 引擎识别流程
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="../%E5%8C%BB%E7%96%97%E5%90%8E%E7%AB%AF/Smart-Merge.html">
<span class="md-ellipsis">
    Smart Merge
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_2" id="__nav_5_2_label" tabindex="">
<span class="md-ellipsis">
    薬師丸賢太
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_2_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_2">
<span class="md-nav__icon md-icon"></span>
            薬師丸賢太
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E8%96%AC%E5%B8%AB%E4%B8%B8%E8%B3%A2%E5%A4%AA/%E5%A4%84%E6%96%B9%E7%AC%BA%E4%BF%9D%E5%AD%98%E5%8C%B9%E9%85%8D%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    处方笺保存匹配流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--section md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3" id="__nav_5_3_label" tabindex="">
<span class="md-ellipsis">
    スマート薬局
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_3">
<span class="md-nav__icon md-icon"></span>
            スマート薬局
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_1" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_1" id="__nav_5_3_1_label" tabindex="0">
<span class="md-ellipsis">
    薬師丸撫子
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_1_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_1">
<span class="md-nav__icon md-icon"></span>
            薬師丸撫子
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E8%96%AC%E5%B8%AB%E4%B8%B8%E6%92%AB%E5%AD%90/%E6%89%AB%E6%8F%8F%E4%BB%AA%E8%BF%9E%E6%8E%A5%E7%BB%93%E6%9E%9C%E8%8E%B7%E5%8F%96%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    扫描仪连接结果获取流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_5_3_2" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_3_2" id="__nav_5_3_2_label" tabindex="0">
<span class="md-ellipsis">
    通用模块
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_5_3_2_label" class="md-nav" data-md-level="3">
<label class="md-nav__title" for="__nav_5_3_2">
<span class="md-nav__icon md-icon"></span>
            通用模块
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../%E3%82%B9%E3%83%9E%E3%83%BC%E3%83%88%E8%96%AC%E5%B1%80/%E9%80%9A%E7%94%A8%E6%A8%A1%E5%9D%97/%E6%97%A5%E5%BF%97%E4%B8%8A%E4%BC%A0%E5%AE%A2%E6%88%B7%E7%AB%AF%E6%B5%81%E7%A8%8B.html">
<span class="md-ellipsis">
    日志上传客户端流程
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
<input checked="" class="md-nav__toggle md-toggle" id="__nav_5_4" type="checkbox"/>
<label class="md-nav__link" for="__nav_5_4" id="__nav_5_4_label" tabindex="">
<span class="md-ellipsis">
    自动化运维
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="true" aria-labelledby="__nav_5_4_label" class="md-nav" data-md-level="2">
<label class="md-nav__title" for="__nav_5_4">
<span class="md-nav__icon md-icon"></span>
            自动化运维
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item md-nav__item--active">
<input class="md-nav__toggle md-toggle" id="__toc" type="checkbox"/>
<label class="md-nav__link md-nav__link--active" for="__toc">
<span class="md-ellipsis">
    medical-backend
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<a class="md-nav__link md-nav__link--active" href="medical-backend.html">
<span class="md-ellipsis">
    medical-backend
    
  </span>
</a>
<nav aria-label="目录" class="md-nav md-nav--secondary">
<label class="md-nav__title" for="__toc">
<span class="md-nav__icon md-icon"></span>
      目录
    </label>
<ul class="md-nav__list" data-md-component="toc" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="#_1">
<span class="md-ellipsis">
      项目概述
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_2">
<span class="md-ellipsis">
      工作流程图
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_3">
<span class="md-ellipsis">
      主要组件说明
    </span>
</a>
<nav aria-label="主要组件说明" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#dependencies">
<span class="md-ellipsis">
      依赖角色 (Dependencies)
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#core-roles">
<span class="md-ellipsis">
      核心角色 (Core Roles)
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_4">
<span class="md-ellipsis">
      使用示例
    </span>
</a>
<nav aria-label="使用示例" class="md-nav">
<ul class="md-nav__list">
<li class="md-nav__item">
<a class="md-nav__link" href="#_5">
<span class="md-ellipsis">
      基本脚本执行
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_6">
<span class="md-ellipsis">
      监控服务升级
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_7">
<span class="md-ellipsis">
      服务重启
    </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_8">
<span class="md-ellipsis">
      数据库操作
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="#_9">
<span class="md-ellipsis">
      关键特性
    </span>
</a>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="performance.html">
<span class="md-ellipsis">
    performance
    
  </span>
</a>
</li>
<li class="md-nav__item">
<a class="md-nav__link" href="terraform.html">
<span class="md-ellipsis">
    terraform
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</li>
<li class="md-nav__item md-nav__item--nested">
<input class="md-nav__toggle md-toggle md-toggle--indeterminate" id="__nav_6" type="checkbox"/>
<label class="md-nav__link" for="__nav_6" id="__nav_6_label" tabindex="0">
<span class="md-ellipsis">
    关于
    
  </span>
<span class="md-nav__icon md-icon"></span>
</label>
<nav aria-expanded="false" aria-labelledby="__nav_6_label" class="md-nav" data-md-level="1">
<label class="md-nav__title" for="__nav_6">
<span class="md-nav__icon md-icon"></span>
            关于
          </label>
<ul class="md-nav__list" data-md-scrollfix="">
<li class="md-nav__item">
<a class="md-nav__link" href="../../%E5%85%B3%E4%BA%8E/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E/release-notes.html">
<span class="md-ellipsis">
    版本说明
    
  </span>
</a>
</li>
</ul>
</nav>
</li>
</ul>
</nav>
</div>
</div>
</div>
<div class="md-content" data-md-component="content">
<article class="md-content__inner md-typeset">
<h1 id="medical-backend-infrastructure-workflow">Medical Backend Infrastructure Workflow<a class="headerlink" href="#medical-backend-infrastructure-workflow" title="Permanent link">¶</a></h1>
<h2 id="_1">项目概述<a class="headerlink" href="#_1" title="Permanent link">¶</a></h2>
<p>Medical Backend Infrastructure Playbook 是一个 Ansible 框架，用于管理医疗后端服务，包括脚本执行、监控服务、数据库操作和服务重启功能。</p>
<h2 id="_2">工作流程图<a class="headerlink" href="#_2" title="Permanent link">¶</a></h2>
<div class="mermaid">flowchart TD
    Start([开始执行 Medical Backend Playbook]) --&gt; ValidateParams{验证 target_group 参数}

    ValidateParams --&gt;|参数无效| Error1[显示错误信息&lt;br/&gt;列出可用的组]
    ValidateParams --&gt;|参数有效| SetupDeps[设置依赖角色]

    Error1 --&gt; End1([执行失败])

    SetupDeps --&gt; SetFacts[Dep.SetFacts&lt;br/&gt;设置全局变量和配置]
    SetFacts --&gt; SshConn[Dep.SshConnection&lt;br/&gt;配置SSH连接]
    SshConn --&gt; CommonRole[01-common&lt;br/&gt;通用任务处理]

    CommonRole --&gt; ScriptCheck{检查是否需要执行脚本}
    ScriptCheck --&gt;|script_name 已定义| CopyBaseScripts[复制基础脚本到远程主机]
    ScriptCheck --&gt;|script_name 未定义| SkipScript[跳过脚本执行]

    CopyBaseScripts --&gt; CopyMainScript[复制主脚本到远程主机]
    CopyMainScript --&gt; ExecuteScript[执行脚本&lt;br/&gt;包含超时和错误处理]
    ExecuteScript --&gt; LogResults[记录执行结果和日志]

    LogResults --&gt; CheckOtherTasks{检查其他任务标签}
    SkipScript --&gt; CheckOtherTasks

    CheckOtherTasks --&gt;|grafana 标签| GrafanaRole[02-grafana&lt;br/&gt;监控服务管理]
    CheckOtherTasks --&gt;|database 标签| DatabaseRole[03-database&lt;br/&gt;数据库操作]
    CheckOtherTasks --&gt;|restart_services 标签| RestartRole[04-restart-services&lt;br/&gt;服务重启]
    CheckOtherTasks --&gt;|无其他标签| Complete[任务完成]

    %% Grafana 分支
    GrafanaRole --&gt; GrafanaChoice{选择 Grafana 操作}
    GrafanaChoice --&gt;|upgrade_services| UpgradeServices[升级监控服务&lt;br/&gt;同步 docker-compose.yml&lt;br/&gt;更新服务版本]
    GrafanaChoice --&gt;|update_configs| UpdateConfigs[更新配置文件&lt;br/&gt;node-exporter.yml&lt;br/&gt;mongodb-exporter.yml&lt;br/&gt;redis-monitor.conf&lt;br/&gt;loki-config.yaml&lt;br/&gt;promtail-config.yaml]

    UpgradeServices --&gt; RestartMonitoring[重启相关监控服务]
    UpdateConfigs --&gt; RestartMonitoring
    RestartMonitoring --&gt; GrafanaComplete[Grafana 任务完成]

    %% Database 分支
    DatabaseRole --&gt; DatabaseOps[执行数据库操作&lt;br/&gt;MongoDB 认证更新&lt;br/&gt;字符串替换操作]
    DatabaseOps --&gt; DatabaseComplete[数据库任务完成]

    %% Restart Services 分支
    RestartRole --&gt; ParseGroups[解析 restart_groups 变量]
    ParseGroups --&gt; ParseServices[解析 restart_services 变量]
    ParseServices --&gt; LoadServiceInfo[加载服务配置信息]
    LoadServiceInfo --&gt; CollectHosts[收集目标主机列表]
    CollectHosts --&gt; CreateTargetGroup[创建 restart_targets 组]
    CreateTargetGroup --&gt; ExecuteRestart[执行服务重启操作]
    ExecuteRestart --&gt; RestartComplete[服务重启完成]

    %% 汇聚点
    GrafanaComplete --&gt; Complete
    DatabaseComplete --&gt; Complete
    RestartComplete --&gt; Complete
    Complete --&gt; End2([执行成功])

    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef role fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px

    class Start,End1,End2 startEnd
    class SetFacts,SshConn,CopyBaseScripts,CopyMainScript,ExecuteScript,LogResults,UpgradeServices,UpdateConfigs,RestartMonitoring,DatabaseOps,ParseGroups,ParseServices,LoadServiceInfo,CollectHosts,CreateTargetGroup,ExecuteRestart process
    class ValidateParams,ScriptCheck,CheckOtherTasks,GrafanaChoice decision
    class Error1 error
    class CommonRole,GrafanaRole,DatabaseRole,RestartRole role
</div>
<h2 id="_3">主要组件说明<a class="headerlink" href="#_3" title="Permanent link">¶</a></h2>
<h3 id="dependencies">依赖角色 (Dependencies)<a class="headerlink" href="#dependencies" title="Permanent link">¶</a></h3>
<ul>
<li><strong>Dep.SetFacts</strong>: 设置全局变量和配置，包括路径、超时设置等</li>
<li><strong>Dep.SshConnection</strong>: 管理SSH连接配置和known_hosts</li>
<li><strong>Dep.RepoOps</strong>: 处理Git仓库操作和同步</li>
</ul>
<h3 id="core-roles">核心角色 (Core Roles)<a class="headerlink" href="#core-roles" title="Permanent link">¶</a></h3>
<ul>
<li><strong>01-common</strong>: 通用任务处理，主要负责脚本执行</li>
<li><strong>02-grafana</strong>: 监控服务管理，包括服务升级和配置更新</li>
<li><strong>03-database</strong>: 数据库操作，主要是MongoDB相关操作</li>
<li><strong>04-restart-services</strong>: 服务重启管理，支持组和单个服务重启</li>
</ul>
<h2 id="_4">使用示例<a class="headerlink" href="#_4" title="Permanent link">¶</a></h2>
<h3 id="_5">基本脚本执行<a class="headerlink" href="#_5" title="Permanent link">¶</a></h3>
<pre><code class="language-bash">ansible-playbook site.yml -e "script_name=health_check.sh target_group=medical_servers"
</code></pre>
<h3 id="_6">监控服务升级<a class="headerlink" href="#_6" title="Permanent link">¶</a></h3>
<pre><code class="language-bash">ansible-playbook site.yml --tags grafana,upgrade_services
</code></pre>
<h3 id="_7">服务重启<a class="headerlink" href="#_7" title="Permanent link">¶</a></h3>
<pre><code class="language-bash">ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']"
</code></pre>
<h3 id="_8">数据库操作<a class="headerlink" href="#_8" title="Permanent link">¶</a></h3>
<pre><code class="language-bash">ansible-playbook site.yml --tags database -e "@extra-vars/database.yml"
</code></pre>
<h2 id="_9">关键特性<a class="headerlink" href="#_9" title="Permanent link">¶</a></h2>
<ul>
<li><strong>健壮的脚本执行</strong>: 包含错误处理、超时管理和适当的退出码管理</li>
<li><strong>幂等操作</strong>: 可安全地多次运行，结果一致</li>
<li><strong>全面的日志记录</strong>: 详细的执行日志，包含时间戳和状态跟踪</li>
<li><strong>健康监控</strong>: 内置健康检查和资源监控</li>
<li><strong>灵活的目标定位</strong>: 可在特定的清单组上执行脚本</li>
<li><strong>备份管理</strong>: 自动备份和清理旧的执行日志</li>
</ul>
</article>
</div>
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
</div>
<button class="md-top md-icon" data-md-component="top" hidden="" type="button">
<svg viewbox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"></path></svg>
  回到页面顶部
</button>
</main>
<footer class="md-footer">
<div class="md-footer-meta md-typeset">
<div class="md-footer-meta__inner md-grid">
<div class="md-copyright">
<div class="md-copyright__highlight">
      Copyright © 2025 SongLin Lu. All rights reserved.
    </div>
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" rel="noopener" target="_blank">
      Material for MkDocs
    </a>
</div>
</div>
</div>
</footer>
</div>
<div class="md-dialog" data-md-component="dialog">
<div class="md-dialog__inner md-typeset"></div>
</div>
<script id="__config" type="application/json">{"base": "../..", "features": ["navigation.sections", "navigation.expand", "navigation.tabs", "navigation.top", "navigation.tracking", "toc.follow", "toc.integrate"], "search": "../../assets/javascripts/workers/search.d50fe291.min.js", "tags": null, "translations": {"clipboard.copied": "\u5df2\u590d\u5236", "clipboard.copy": "\u590d\u5236", "search.result.more.one": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.more.other": "\u5728\u8be5\u9875\u4e0a\u8fd8\u6709 # \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.none": "\u6ca1\u6709\u627e\u5230\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.one": "\u627e\u5230 1 \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.other": "# \u4e2a\u7b26\u5408\u6761\u4ef6\u7684\u7ed3\u679c", "search.result.placeholder": "\u952e\u5165\u4ee5\u5f00\u59cb\u641c\u7d22", "search.result.term.missing": "\u7f3a\u5c11", "select.version": "\u9009\u62e9\u5f53\u524d\u7248\u672c"}, "version": null}</script>
<script src="../../assets/javascripts/bundle.56ea9cef.min.js"></script>
<script type="module">import mermaid from "https://unpkg.com/mermaid@10.4.0/dist/mermaid.esm.min.mjs";
mermaid.initialize({});</script></body>
</html>